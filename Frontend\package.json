{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently --names \"VI<PERSON>,<PERSON>YTH<PERSON>,NODE\" --prefix-colors \"cyan,yellow,green\" \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:server\"", "dev:frontend": "vite", "dev:backend": "cd ../Backend && python app.py", "dev:server": "node server.js", "dev:alt": "npm-run-all --parallel dev:frontend dev:backend dev:server", "dev:custom": "node scripts/start-dev.js", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "node server.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google/generative-ai": "^0.24.1", "@hello-pangea/dnd": "^18.0.1", "@iconify/react": "^6.0.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "framer-motion": "^12.9.4", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.1", "pg": "^8.15.6", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-confetti-explosion": "^2.1.2", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-gauge-component": "^1.2.64", "react-icons": "^5.5.0", "react-router-dom": "^6.30.0", "react-spinners": "^0.17.0", "react-toastify": "^11.0.5", "sass": "^1.87.0", "scss": "^0.2.4", "simplex-noise": "^4.0.3", "styled-components": "^6.1.18", "three": "^0.176.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "concurrently": "^8.2.2", "npm-run-all": "^4.1.5", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}